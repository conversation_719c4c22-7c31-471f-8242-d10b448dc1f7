import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from database import get_database_engine
import logging
from sqlalchemy import text

logger = logging.getLogger(__name__)


class YouTubeAnalyzer:
    def __init__(self):
        self.engine = get_database_engine()

    def get_channel_analytics(self):
        """Get basic channel analytics"""
        query = """
            SELECT 
                channel_name,
                subscriber_count,
                total_views,
                total_videos,
                ROUND(total_views::numeric / NULLIF(subscriber_count, 0), 2) as views_per_subscriber,
                ROUND(total_views::numeric / NULLIF(total_videos, 0), 2) as avg_views_per_video
            FROM dim_channel
            ORDER BY subscriber_count DESC
        """

        # SỬA: Sử dụng engine.connect() đúng cách
        with self.engine.connect() as conn:
            # Chuyển connection thành SQLAlchemy connection object
            result = conn.execute(text(query))
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
        return df

    def get_growth_analysis(self):
        """Analyze channel growth patterns"""
        query = text("""
            SELECT 
                channel_name,
                subscriber_count,
                total_views,
                CASE 
                    WHEN subscriber_count > 10000000 THEN 'Mega Channel'
                    WHEN subscriber_count > 1000000 THEN 'Large Channel' 
                    WHEN subscriber_count > 100000 THEN 'Medium Channel'
                    ELSE 'Small Channel'
                END as channel_size,
                RANK() OVER (ORDER BY subscriber_count DESC) as subscriber_rank,
                RANK() OVER (ORDER BY total_views DESC) as views_rank
            FROM dim_channel
        """)

        with self.engine.connect() as conn:
            result = conn.execute(query)
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
        return df

    def create_visualizations(self):
        """Create advanced visualizations"""
        # Get data
        channel_df = self.get_channel_analytics()

        if channel_df.empty:
            logger.warning("No data available for visualization")
            return

        # 🔥 FIX: Convert numeric columns từ string sang number
        numeric_columns = ['subscriber_count', 'total_views', 'total_videos', 
                        'views_per_subscriber', 'avg_views_per_video']
        
        for col in numeric_columns:
            if col in channel_df.columns:
                # Convert sang numeric, errors='coerce' sẽ chuyển giá trị lỗi thành NaN
                channel_df[col] = pd.to_numeric(channel_df[col], errors='coerce')
        
        # Create subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # Plot 1: Subscriber Count
        channel_df.plot(kind='bar', x='channel_name', y='subscriber_count',
                        ax=ax1, title='Subscriber Count by Channel', color='skyblue')
        ax1.tick_params(axis='x', rotation=45)

        # Plot 2: Total Views
        channel_df.plot(kind='bar', x='channel_name', y='total_views',
                        ax=ax2, title='Total Views by Channel', color='lightgreen')
        ax2.tick_params(axis='x', rotation=45)

        # Plot 3: Views per Subscriber
        channel_df.plot(kind='bar', x='channel_name', y='views_per_subscriber',
                        ax=ax3, title='Views per Subscriber', color='orange')
        ax3.tick_params(axis='x', rotation=45)

        # Plot 4: Avg Views per Video
        channel_df.plot(kind='bar', x='channel_name', y='avg_views_per_video',
                        ax=ax4, title='Average Views per Video', color='lightcoral')
        ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig('channel_analytics.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Visualizations created successfully!")

        return channel_df

    def generate_report(self):
        """Generate comprehensive analytics report"""
        analysis_df = self.get_growth_analysis()

        if analysis_df.empty:
            return "No data available for report"

        report = []
        report.append("📊 YOUTUBE CHANNEL ANALYTICS REPORT")
        report.append("=" * 50)

        for _, row in analysis_df.iterrows():
            report.append(
                f"Channel: {row['channel_name']}\n"
                f"  - Subscribers: {row['subscriber_count']:,}\n"
                f"  - Total Views: {row['total_views']:,}\n"
                f"  - Size Category: {row['channel_size']}\n"
                f"  - Subscriber Rank: #{row['subscriber_rank']}\n"
                f"  - Views Rank: #{row['views_rank']}\n"
            )

        return "\n".join(report)


# Test the analysis
if __name__ == "__main__":
    analyzer = YouTubeAnalyzer()

    # Generate report
    report = analyzer.generate_report()
    print(report)

    # Create visualizations
    analyzer.create_visualizations()
    print("✅ Visualizations saved as 'channel_analytics.png'")
