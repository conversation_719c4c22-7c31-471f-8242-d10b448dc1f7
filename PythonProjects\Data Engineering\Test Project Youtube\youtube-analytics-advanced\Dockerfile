FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY scripts/ ./scripts/
COPY .env .

# Create directory for Airflow
RUN mkdir -p /app/airflow/dags

# Expose port for Airflow
EXPOSE 8080

# Default command (can be overridden)
CMD ["python", "scripts/advanced_etl.py"]