# from scripts.advanced_etl import YouTubeAnalyticsETL
import pytest
from unittest.mock import Mock, patch
import pandas as pd
import sys
import os

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Now import
try:
    from scripts.advanced_etl import YouTubeAnalyticsETL
    print("✅ Import successful!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print(f"Project root: {project_root}")
    print("Python path:")
    for p in sys.path:
        print(f"  - {p}")
    exit(1)

class TestYouTubeAnalyticsETL:

    def test_transform_channel_data_valid(self):
        """Test transform function with valid data"""
        etl = YouTubeAnalyticsETL()

        # Mock raw data from YouTube API
        mock_raw_data = {
            'items': [{
                'id': 'test_channel_123',
                'snippet': {
                    'title': 'Test Channel'
                },
                'statistics': {
                    'subscriberCount': '1000000',
                    'viewCount': '50000000',
                    'videoCount': '1000'
                }
            }]
        }

        result = etl.transform_channel_data(mock_raw_data)

        assert result is not None
        assert result['channel_id'] == 'test_channel_123'
        assert result['channel_name'] == 'Test Channel'
        assert result['subscriber_count'] == 1000000
        assert result['total_views'] == 50000000
        assert result['total_videos'] == 1000
        print("✅ test_transform_channel_data_valid passed!")

    def test_transform_channel_data_invalid(self):
        """Test transform function with invalid data"""
        etl = YouTubeAnalyticsETL()

        # Test with None data
        assert etl.transform_channel_data(None) is None

        # Test with empty data
        assert etl.transform_channel_data({}) is None

        # Test with missing items
        assert etl.transform_channel_data({'items': []}) is None
        print("✅ test_transform_channel_data_invalid passed!")

    @patch('scripts.advanced_etl.requests.get')
    def test_extract_channel_data_success(self, mock_get):
        """Test successful API extraction"""
        etl = YouTubeAnalyticsETL()

        # Mock API response
        mock_response = Mock()
        mock_response.json.return_value = {
            'items': [{'test': 'data'}]
        }
        mock_get.return_value = mock_response

        result = etl.extract_channel_data('test_channel')

        assert result is not None
        mock_get.assert_called_once()
        print("✅ test_extract_channel_data_success passed!")

    @patch('scripts.advanced_etl.requests.get')
    def test_extract_channel_data_failure(self, mock_get):
        """Test API extraction failure"""
        etl = YouTubeAnalyticsETL()

        mock_get.side_effect = Exception("API Error")

        result = etl.extract_channel_data('test_channel')

        assert result is None
        print("✅ test_extract_channel_data_failure passed!")


def run_tests():
    """Run all tests"""
    test_instance = TestYouTubeAnalyticsETL()

    print("🧪 Running YouTube Analytics ETL Tests...")
    print("=" * 50)

    # Run each test method
    test_instance.test_transform_channel_data_valid()
    test_instance.test_transform_channel_data_invalid()
    test_instance.test_extract_channel_data_success()
    test_instance.test_extract_channel_data_failure()

    print("=" * 50)
    print("🎉 All tests passed!")


if __name__ == "__main__":
    run_tests()
