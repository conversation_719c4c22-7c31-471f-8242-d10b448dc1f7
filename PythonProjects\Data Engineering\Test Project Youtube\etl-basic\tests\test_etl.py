# tests/test_etl.py
import pandas as pd
from etl import transform_video_data

def test_transform_video_data():
    # Tạo dữ liệu giả (mock data) gi<PERSON>ng với cấu trúc từ API
    mock_raw_data = [
        {
            'id': {'videoId': 'abc123'},
            'snippet': {
                'title': 'Test Video',
                'description': 'A test description',
                'publishedAt': '2023-10-15T12:00:00Z',
                'channelTitle': 'Test Channel'
            }
        }
    ]
    
    # Gọi hàm transform
    result_df = transform_video_data(mock_raw_data)
    
    # Kiể<PERSON> tra kết quả
    assert isinstance(result_df, pd.DataFrame)
    assert len(result_df) == 1
    assert result_df.iloc[0]['video_id'] == 'abc123'
    assert result_df.iloc[0]['title'] == 'Test Video'
    assert result_df.iloc[0]['channel_title'] == 'Test Channel'
    # Kiểm tra kiểu dữ liệu của cộ<PERSON> published_at
    assert pd.api.types.is_datetime64_any_dtype(result_df['published_at'])
    
    print("✅ All tests passed!")

if __name__ == "__main__":
    test_transform_video_data()