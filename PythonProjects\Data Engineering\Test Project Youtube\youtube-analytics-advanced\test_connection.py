# test_connection.py
from scripts.database import get_database_engine


def test_connection():
    try:
        engine = get_database_engine()
        with engine.connect() as conn:
            result = conn.execute("SELECT NOW() as current_time;")
            print(f"✅ Database connection successful! Time: {result.scalar()}")
    except Exception as e:
        print(f"❌ Connection failed: {e}")


if __name__ == "__main__":
    test_connection()
