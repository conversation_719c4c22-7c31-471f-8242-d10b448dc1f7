version: '3.8'
services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: youtube_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Tuquy19031999!
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  airflow:
    build: .
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__CORE__SQL_ALCHEMY_CONN: postgresql+psycopg2://postgres:your_secure_password_123@db:5432/airflow
      AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'false'
    volumes:
      - ./airflow/dags:/app/airflow/dags
      - ./airflow/logs:/app/airflow/logs
      - ./airflow/plugins:/app/airflow/plugins
      - ./scripts:/app/scripts
      - ./tests:/app/tests
      - ./notebooks:/app/notebooks
    ports:
      - "8080:8080"
    depends_on:
      - db
    command: >
      bash -c "
      airflow db init &&
      airflow users create --username admin --password admin --firstname Admin --lastname User --role Admin --email <EMAIL> &&
      airflow webserver & airflow scheduler
      "

volumes:
  postgres_data: