import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import exc, text
import logging
import os
from dotenv import load_dotenv
from database import get_database_engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()


class YouTubeAnalyticsETL:
    def __init__(self):
        self.api_key = os.getenv('YOUTUBE_API_KEY')
        self.engine = get_database_engine()

    def extract_channel_data(self, channel_id):
        """Extract data from a single channel"""
        try:
            logger.info(f"Extracting data for channel: {channel_id}")

            # Get channel statistics
            channel_url = "https://www.googleapis.com/youtube/v3/channels"
            params = {
                'part': 'snippet,statistics',
                'id': channel_id,
                'key': self.api_key
            }
            response = requests.get(channel_url, params=params)
            channel_info = response.json()

            return channel_info

        except Exception as e:
            logger.error(
                f"Error extracting data for channel {channel_id}: {e}")
            return None

    def transform_channel_data(self, raw_data):
        """Transform channel data"""
        if not raw_data or 'items' not in raw_data or not raw_data['items']:
            return None

        channel_info = raw_data['items'][0]
        transformed = {
            'channel_id': channel_info['id'],
            'channel_name': channel_info['snippet']['title'],
            'subscriber_count': int(channel_info['statistics'].get('subscriberCount', 0)),
            'total_views': int(channel_info['statistics'].get('viewCount', 0)),
            'total_videos': int(channel_info['statistics'].get('videoCount', 0))
        }
        return transformed

    def load_channel_data(self, channel_data):
        """Load channel data into dim_channel using raw SQL"""
        if not channel_data:
            return

        try:
            # SỬ DỤNG SQL THUẦN - không dùng pandas
            with self.engine.connect() as conn:
                # SỬA: Sử dụng begin() để tự động quản lý transaction
                with conn.begin():
                    conn.execute(text("""
                        INSERT INTO dim_channel (channel_id, channel_name, subscriber_count, total_views, total_videos)
                        VALUES (:channel_id, :channel_name, :subscriber_count, :total_views, :total_videos)
                        ON CONFLICT (channel_id) DO UPDATE SET
                            channel_name = EXCLUDED.channel_name,
                            subscriber_count = EXCLUDED.subscriber_count,
                            total_views = EXCLUDED.total_views,
                            total_videos = EXCLUDED.total_videos
                    """), {
                        'channel_id': channel_data['channel_id'],
                        'channel_name': channel_data['channel_name'],
                        'subscriber_count': channel_data['subscriber_count'],
                        'total_views': channel_data['total_views'],
                        'total_videos': channel_data['total_videos']
                    })
                # XÓA DÒNG conn.commit() - không cần thiết nữa

            logger.info(
                f"✅ Loaded channel data for {channel_data['channel_name']}")

        except Exception as e:
            logger.error(f"❌ Error loading channel data: {e}")

    def run_simple_etl(self, channel_id):
        """Run a simple ETL for one channel"""
        logger.info("Starting simple ETL process...")

        # Extract
        raw_data = self.extract_channel_data(channel_id)
        if not raw_data:
            return False

        # Transform
        channel_data = self.transform_channel_data(raw_data)
        if not channel_data:
            return False

        # Load
        self.load_channel_data(channel_data)

        logger.info("Simple ETL process completed!")
        return True


# Test the ETL
if __name__ == "__main__":
    etl = YouTubeAnalyticsETL()

    # Test với channel TED Talks
    success = etl.run_simple_etl('UCAuUUnT6oDeKwE6v1NGQxug')

    if success:
        print("✅ ETL test successful! Check the database.")
    else:
        print("❌ ETL test failed.")
