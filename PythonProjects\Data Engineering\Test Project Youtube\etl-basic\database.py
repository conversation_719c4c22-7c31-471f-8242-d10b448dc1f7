from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, DateTime, Text
import os
from dotenv import load_dotenv

load_dotenv()

# <PERSON>ết n<PERSON>i đến database sử dụng biến môi trường
def get_database_engine():
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_host = os.getenv('DB_HOST')
    db_port = os.getenv('DB_PORT')
    db_name = os.getenv('DB_NAME')
    
    connection_string = f"postgresql+psycopg2://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    engine = create_engine(connection_string)
    return engine

# Đ<PERSON>nh nghĩa cấu trúc bảng 'videos'
def create_tables(engine):
    metadata = MetaData()
    
    Table('videos', metadata,
          Column('id', Integer, primary_key=True, autoincrement=True),
          Column('video_id', String(255), unique=True),
          <PERSON>umn('title', Text),
          Column('description', Text),
          <PERSON>umn('published_at', DateTime),
          Column('channel_title', String(255))
    )
    
    metadata.create_all(engine)
    print("Tables created successfully!")