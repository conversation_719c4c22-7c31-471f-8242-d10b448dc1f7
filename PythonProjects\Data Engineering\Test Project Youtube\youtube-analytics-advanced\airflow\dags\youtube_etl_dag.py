from scripts.advanced_etl import YouTubeAnalyticsETL
from scripts.advanced_analysis import <PERSON><PERSON>nal<PERSON>zer
import sys
import os
from datetime import datetime, timedelta

# Add scripts directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../scripts'))

try:
    from airflow import DAG
    from airflow.operators.python import PythonOperator
    from airflow.operators.bash import BashOperator
    AIRFLOW_AVAILABLE = True
except ImportError:
    AIRFLOW_AVAILABLE = False
    print("⚠️ Airflow not installed - this is normal during development")

# Import your custom modules


default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}


def run_youtube_etl():
    """Run YouTube ETL process"""
    etl = YouTubeAnalyticsETL()

    # List of channels to process
    channels = [
        'UCAuUUnT6oDeKwE6v1NGQxug',  # TED
        'UCsT0YIqwnpJCM-mx7-gSA4Q',  # TED-Ed
        'UC_x5XG1OV2P6uZZ5FSM9Ttw',  # Google
        'UCzuqhhs6NWbgTzMuM09WKDQ',  # Netflix
    ]

    for channel_id in channels:
        etl.run_simple_etl(channel_id)


def run_analysis():
    """Run data analysis and reporting"""
    analyzer = YouTubeAnalyzer()

    # Generate report
    report = analyzer.generate_report()
    print(report)

    # Create visualizations
    analyzer.create_visualizations()


with DAG(
    'youtube_analytics_pipeline',
    default_args=default_args,
    description='Complete YouTube Analytics ETL and Analysis Pipeline',
    schedule_interval=timedelta(hours=6),  # Run every 6 hours
    catchup=False,
    tags=['youtube', 'analytics', 'etl'],
) as dag:

    run_etl_task = PythonOperator(
        task_id='run_youtube_etl',
        python_callable=run_youtube_etl,
    )

    run_analysis_task = PythonOperator(
        task_id='run_analysis',
        python_callable=run_analysis,
    )

    test_task = BashOperator(
        task_id='run_tests',
        bash_command='cd /app && python -m pytest tests/ -v',
    )

    # Set dependencies
    run_etl_task >> run_analysis_task >> test_task
