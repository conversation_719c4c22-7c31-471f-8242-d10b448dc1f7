{"cells": [{"cell_type": "code", "execution_count": null, "id": "9552c8d0", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# cells trong analysis.ipynb\n", "\n", "# Cell 1: Import\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sqlalchemy import create_engine\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "# Cell 2: <PERSON><PERSON><PERSON> database\n", "def get_engine():\n", "    db_user = os.getenv('DB_USER')\n", "    db_password = os.getenv('DB_PASSWORD') \n", "    db_host = os.getenv('DB_HOST')\n", "    db_port = os.getenv('DB_PORT')\n", "    db_name = os.getenv('DB_NAME')\n", "    \n", "    connection_string = f\"postgresql+psycopg2://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}\"\n", "    return create_engine(connection_string)\n", "\n", "engine = get_engine()\n", "\n", "# Cell 3: Load data\n", "channel_df = pd.read_sql(\"SELECT * FROM dim_channel\", engine)\n", "print(\"Channel Data:\")\n", "print(channel_df)\n", "\n", "# Cell 4: Basic analysis\n", "print(\"\\n📊 Basic Statistics:\")\n", "print(f\"Total Channels: {len(channel_df)}\")\n", "print(f\"Total Subscribers: {channel_df['subscriber_count'].sum():,}\")\n", "print(f\"Total Views: {channel_df['total_views'].sum():,}\")\n", "\n", "# Cell 5: Visualizations\n", "plt.figure(figsize=(12, 6))\n", "channel_df.plot(kind='bar', x='channel_name', y='subscriber_count')\n", "plt.title('Subscriber Count by Channel')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}