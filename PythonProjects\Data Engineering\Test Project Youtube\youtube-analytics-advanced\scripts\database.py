import os
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Float, DateTime, Date, Text, BigInteger
from dotenv import load_dotenv

load_dotenv()

def get_database_engine():
    """Kết nối đến database - c<PERSON>ch cũ tương thích tốt"""
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_host = os.getenv('DB_HOST')
    db_port = os.getenv('DB_PORT')
    db_name = os.getenv('DB_NAME')
    
    connection_string = f"postgresql+psycopg2://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    engine = create_engine(connection_string)
    return engine

def create_advanced_tables():
    """Tạo các tables cho data warehouse"""
    engine = get_database_engine()
    metadata = MetaData()
    
    # Dimension Tables
    Table('dim_date', metadata,
          Column('date_id', Integer, primary_key=True, autoincrement=True),
          Column('date', Date, unique=True),
          Column('day', Integer),
          Column('month', Integer),
          Column('year', Integer),
          Column('quarter', Integer),
          Column('day_of_week', Integer)
    )
    
    Table('dim_channel', metadata,
          Column('channel_id', String(255), primary_key=True),
          Column('channel_name', String(255)),
          Column('subscriber_count', Integer),
          Column('total_views', BigInteger),
          Column('total_videos', BigInteger)
    )
    
    Table('dim_video', metadata,
          Column('video_id', String(255), primary_key=True),
          Column('channel_id', String(255)),
          Column('title', Text),
          Column('description', Text),
          Column('published_at', DateTime),
          Column('duration', String(50)),
          Column('category_id', String(50))
    )
    
    # Fact Table
    Table('fact_video_stats', metadata,
          Column('id', Integer, primary_key=True, autoincrement=True),
          Column('date', Date),
          Column('video_id', String(255)),
          Column('views', BigInteger),
          Column('likes', Integer),
          Column('dislikes', Integer),
          Column('comments', Integer),
          Column('estimated_earnings', Float)
    )
    
    try:
        metadata.create_all(engine)
        print("✅ Advanced tables created successfully!")
    except Exception as e:
        print(f"❌ Error creating tables: {e}")

if __name__ == "__main__":
    create_advanced_tables()