from database import get_database_engine, create_tables
from etl import extract_videos_from_channel, transform_video_data, load_video_data_to_db

def main():
    print("Starting ETL Pipeline...")
    
    # 1. Khởi tạo database (chạy 1 lần đ<PERSON>u là đủ)
    engine = get_database_engine()
    create_tables(engine)
    
    # 2. ETL Process
    print("Extracting data from YouTube API...")
    raw_data = extract_videos_from_channel()
    
    if raw_data:
        print("Transforming data...")
        clean_data = transform_video_data(raw_data)
        
        print("Loading data to PostgreSQL...")
        load_video_data_to_db(clean_data)
        print("ETL Pipeline completed successfully!")
    else:
        print("Extraction failed. Pipeline stopped.")

if __name__ == "__main__":
    main()