import requests
import pandas as pd
from sqlalchemy import exc
from database import get_database_engine
import os
from dotenv import load_dotenv

load_dotenv()

# --- EXTRACT ---
def extract_videos_from_channel(channel_id='UCAuUUnT6oDeKwE6v1NGQxug'): # Channel ID mặc định: TED Talks
    api_key = os.getenv('YOUTUBE_API_KEY')
    base_url = "https://www.googleapis.com/youtube/v3/search"
    params = {
        'part': 'snippet',
        'channelId': channel_id,
        'maxResults': 10,  # Lấy 10 video gần nhất để thử nghiệm
        'order': 'date',
        'type': 'video',
        'key': api_key
    }
    
    response = requests.get(base_url, params=params)
    
    data = response.json()
    
    if 'items' not in data:
        print("Error or no items found:", data)
        return []
    
    return data['items']

# --- TRANSFORM ---
def transform_video_data(raw_videos):
    video_list = []
    for video in raw_videos:
        snippet = video['snippet']
        video_data = {
            'video_id': video['id']['videoId'],
            'title': snippet['title'],
            'description': snippet['description'],
            'published_at': snippet['publishedAt'],
            'channel_title': snippet['channelTitle']
        }
        video_list.append(video_data)
    
    # Chuyển list thành DataFrame
    df = pd.DataFrame(video_list)
    # Chuyển đổi kiểu dữ liệu cho cột published_at
    df['published_at'] = pd.to_datetime(df['published_at'])
    # print(f"In thử dataframe và pandas: {df.head()}")
    return df

# --- LOAD ---
def load_video_data_to_db(transformed_df):
    engine = get_database_engine()
    try:
        # Ghi DataFrame vào bảng 'videos' trong DB
        # if_exists='append' : thêm dữ liệu mới vào, nếu trùng 'video_id' (do unique constraint) sẽ báo lỗi.
        transformed_df.to_sql('videos', engine, if_exists='append', index=False)
        print(f"Successfully loaded {len(transformed_df)} records to the database.")
    except exc.IntegrityError:
        print("Duplicate video_id found. Some records were skipped.")
    except Exception as e:
        print(f"An error occurred during loading: {e}")